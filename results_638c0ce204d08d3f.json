{"query_id": "638c0ce204d08d3f", "query_details": {"name": "virat", "photo_path": "C:\\Users\\<USER>\\Documents\\MY PROJECTS\\Social search API\\profile.jpg", "platforms_requested": ["instagram"], "timestamp": "2025-07-20T00:51:18.137424"}, "platform_results": {"instagram": {"status": "error", "matches": [], "error": "instagram error: Instagram search failed: instagram error: HTTP error 500: Server error '500 Internal Server Error' for url 'https://rocketapi-for-developers.p.rapidapi.com/instagram/search'\nFor more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500"}}, "image_analysis_result": {"status": "analyzed", "file_path": "C:\\Users\\<USER>\\Documents\\MY PROJECTS\\Social search API\\profile.jpg", "file_type": "image/jpeg", "size_bytes": 754832, "face_count": 1, "profile_pic_matches": [], "error": null}, "summary": {"overall_status": "completed_with_errors", "errors": ["instagram: instagram error: Instagram search failed: instagram error: HTTP error 500: Server error '500 Internal Server Error' for url 'https://rocketapi-for-developers.p.rapidapi.com/instagram/search'\nFor more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500"], "platform_matches": 0, "face_matches": 0, "platform_statuses": {"instagram": "error"}, "image_analysis_status": "analyzed"}, "duration_seconds": 17.772071599960327}